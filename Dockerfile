ARG NODE_VERSION=20
FROM nikolaik/python-nodejs:python3.12-nodejs20-slim

ARG N8N_VERSION
RUN if [ -z "$N8N_VERSION" ] ; then echo "The N8N_VERSION argument is missing!" ; exit 1; fi

LABEL org.opencontainers.image.title="n8n"
LABEL org.opencontainers.image.description="Workflow Automation Tool"
LABEL org.opencontainers.image.source="https://github.com/n8n-io/n8n"
LABEL org.opencontainers.image.url="https://n8n.io"
LABEL org.opencontainers.image.version=${N8N_VERSION}

ENV N8N_VERSION=${N8N_VERSION}
ENV NODE_ENV=production
ENV N8N_RELEASE_TYPE=stable
RUN set -eux; \
	npm install -g --omit=dev n8n@${N8N_VERSION} --ignore-scripts && \
	npm rebuild --prefix=/usr/local/lib/node_modules/n8n sqlite3 && \
	rm -rf /usr/local/lib/node_modules/n8n/node_modules/@n8n/chat && \
	rm -rf /usr/local/lib/node_modules/n8n/node_modules/n8n-design-system && \
	rm -rf /usr/local/lib/node_modules/n8n/node_modules/n8n-editor-ui/node_modules && \
	find /usr/local/lib/node_modules/n8n -type f -name "*.ts" -o -name "*.js.map" -o -name "*.vue" | xargs rm -f && \
	rm -rf /root/.npm

ENV TINI_VERSION v0.19.0
ADD https://github.com/krallin/tini/releases/download/${TINI_VERSION}/tini /tini
RUN chmod +x /tini

ENV N8N_DEFAULT_BINARY_DATA_MODE=filesystem
ENV N8N_BINARY_DATA_STORAGE_PATH=/binary_data
ENV GENERIC_TIMEZONE=America/Los_Angeles
ENV N8N_ENCRYPTION_KEY=RM7V5l4fVqlZbRvFvwRxweSvtnGwdp+t
ENV N8N_LICENSE_ACTIVATION_KEY=7b1a2d13-bc2d-479f-8382-e8fb79473d6b
ENV WEBHOOK_URL=https://control-center.nationsinfocorp.com/

WORKDIR /

COPY docker-entrypoint.sh /
RUN	chmod +x /docker-entrypoint.sh

RUN mkdir requirements && chown pn:pn requirements
COPY requirements/* requirements/
RUN	chmod -R +x requirements

RUN pip install -r requirements/requirements.txt

RUN mkdir py_scripts && chown pn:pn py_scripts
COPY py_scripts/* py_scripts/
RUN	chmod -R +x py_scripts

RUN mkdir sql_scripts && chown pn:pn sql_scripts
COPY sql_scripts/* sql_scripts/
RUN	chmod -R +x sql_scripts

RUN mkdir binary_data && chown pn:pn binary_data
COPY binary_data/* binary_data/
RUN	chmod -R +x binary_data

RUN apt-get update && apt-get -y install sudo openssh-server
RUN mkdir /var/run/sshd && chmod 755 /var/run/sshd
RUN useradd -ms /bin/bash cc && echo "cc:Nationsinfo0!" | chpasswd && adduser cc sudo
RUN apt-get update

RUN mkdir .n8n && chown pn:pn .n8n
ENV SHELL /bin/bash
USER pn

ENTRYPOINT ["/tini", "--", "bash", "/docker-entrypoint.sh"]
