import requests
import sys
from config import <PERSON><PERSON>_<PERSON>OMAIN, MC<PERSON>_CLIENT_ID, MCE_CLIENT_SECRET, MCE_ACCOUNT_ID, MCE_AUTH_URL, MCE_SEND_SMS_URL, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE
from datetime import datetime
import pytz
import psycopg2

# Platform configurations
PLATFORM_CONFIG = {
    'mce_vz': {
        'sms_send_id': 'MjA5OTA6Nzg6MA',
        'platform': 'mce_vz'
    },
    'mce_fcl': {
        'sms_send_id': 'MjA5ODc6Nzg6MA',
        'platform': 'mce_fcl'
    },
    'mce_rto': {
        'sms_send_id': 'MjA5ODg6Nzg6MA',
        'platform': 'mce_rto'
    },
    'mce_crc': {
        'sms_send_id': 'MjA5ODk6Nzg6MA',
        'platform': 'mce_crc'
    }
}

# Global configuration
domain = MCE_DOMAIN
client_id = MCE_CLIENT_ID
client_secret = MCE_CLIENT_SECRET
account_id = MCE_ACCOUNT_ID
auth_url = MCE_AUTH_URL
send_sms_url = MCE_SEND_SMS_URL

def authenticate(client_id, client_secret):
    """Authenticate with Salesforce Marketing Cloud API and return access token."""
    payload = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret,
        'account_id': account_id
    }

    response = requests.post(auth_url, json=payload)
    if response.status_code != 200:
        print("Authentication Error:", response.text)
        raise Exception("Failed to authenticate with Salesforce Marketing Cloud API")

    return response.json().get('access_token')

def send_sms(access_token, phone_number, message, sms_send_id):
    """Send SMS using Marketing Cloud API."""
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    data = {
        "Subscribe": True,
        "Resubscribe": True,
        "mobileNumbers": [phone_number],
        "keyword": "NIC",
        "Override": True,
        "messageText": message
    }

    url = send_sms_url.format(sms_send_id=sms_send_id)
    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 202:
        return {"status": "success", "message": f"{response.status_code} - {response.text}"}
    else:
        return {"status": "error", "message": f"{response.status_code} - {response.text}"}

def get_sms_data_from_db():
    """Retrieve SMS data from database for all platforms with pending messages."""
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        query = """
            SELECT id_sms, phone, email, firstname, lastname, version, message, platform
            FROM subscriber.transaction_sms
            WHERE platform IN ('mce_vz', 'mce_fcl', 'mce_rto', 'mce_crc')
            AND campaign_date = CURRENT_DATE
            AND delivery_status IS NULL
        """
        cursor.execute(query)
        records = cursor.fetchall()

        sms_data = []
        for record in records:
            id_sms, phone, email, firstname, lastname, version, message, platform = record
            sms_data.append({
                'id_sms': id_sms,
                'phone': phone,
                'email': email,
                'firstname': firstname,
                'lastname': lastname,
                'version': version,
                'message': message,
                'platform': platform
            })
        return sms_data

    except Exception as e:
        print("Error reading data from PostgreSQL table:", str(e))
        return []  # Return empty list instead of None
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def update_delivery_status(id_sms, status, delivery_date, response_mce):
    """Update delivery status in the database."""
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        update_query = """
            UPDATE subscriber.transaction_sms
            SET delivery_status = %s, delivery_date = %s, response_mce = %s
            WHERE id_sms = %s
        """
        cursor.execute(update_query, (status, delivery_date, response_mce, id_sms))
        connection.commit()
    except Exception as e:
        print("Error updating delivery status in PostgreSQL table:", str(e))
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def main():
    """Main function to process SMS sending for all platforms automatically."""
    try:
        # Authenticate once for all platforms
        access_token = authenticate(client_id, client_secret)

        # Get all SMS data from database
        sms_data = get_sms_data_from_db()

        if not sms_data:
            print("No SMS data found for any platform")
            return

        # Group SMS data by platform
        platform_groups = {}
        for sms in sms_data:
            platform = sms['platform']
            if platform not in platform_groups:
                platform_groups[platform] = []
            platform_groups[platform].append(sms)

        print(f"Found SMS data for platforms: {', '.join(platform_groups.keys())}")

        # Process each platform
        total_processed = 0
        total_success = 0

        for platform, sms_list in platform_groups.items():
            if platform not in PLATFORM_CONFIG:
                print(f"Warning: Unknown platform '{platform}' found in database. Skipping.")
                continue

            config = PLATFORM_CONFIG[platform]
            print(f"\nProcessing {len(sms_list)} SMS records for platform: {platform}")

            platform_success = 0

            # Process each SMS for this platform
            for sms in sms_list:
                id_sms = sms['id_sms']
                phone = sms['phone']
                message = sms['message']

                # Add 1 prefix to phone number
                formatted_phone = f"1{phone}"
                result = send_sms(access_token, formatted_phone, message, config['sms_send_id'])

                status = result['status']
                response_mce = result['message']
                delivery_date = datetime.now().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('US/Pacific')) if status == "success" else None

                update_delivery_status(id_sms, status, delivery_date, response_mce)

                total_processed += 1
                if status == "success":
                    platform_success += 1
                    total_success += 1

                print(f"  SMS ID {id_sms}: {status}")

            print(f"Platform {platform} complete: {platform_success}/{len(sms_list)} successful")

        print(f"\nOverall processing complete: {total_success}/{total_processed} SMS sent successfully")

    except Exception as e:
        print(f"Error in main processing: {str(e)}")

if __name__ == "__main__":
    main()