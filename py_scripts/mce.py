import requests
import sys
from config import <PERSON><PERSON>_<PERSON>OMAIN, MC<PERSON>_CLIENT_ID, MCE_CLIENT_SECRET, MCE_ACCOUNT_ID, MCE_AUTH_URL, MCE_SEND_SMS_URL, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE
from datetime import datetime
import pytz
import psycopg2

# Platform configurations
PLATFORM_CONFIG = {
    'mce_vz': {
        'sms_send_id': 'MjA5OTA6Nzg6MA',
        'platform': 'mce_vz'
    },
    'mce_fcl': {
        'sms_send_id': 'MjA5ODc6Nzg6MA',
        'platform': 'mce_fcl'
    },
    'mce_rto': {
        'sms_send_id': 'MjA5ODg6Nzg6MA',
        'platform': 'mce_rto'
    },
    'mce_crc': {
        'sms_send_id': 'MjA5ODk6Nzg6MA',
        'platform': 'mce_crc'
    }
}

# Global configuration
domain = MCE_DOMAIN
client_id = MCE_CLIENT_ID
client_secret = MCE_CLIENT_SECRET
account_id = MCE_ACCOUNT_ID
auth_url = MCE_AUTH_URL
send_sms_url = MCE_SEND_SMS_URL

def authenticate(client_id, client_secret):
    """Authenticate with Salesforce Marketing Cloud API and return access token."""
    payload = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret,
        'account_id': account_id
    }

    response = requests.post(auth_url, json=payload)
    if response.status_code != 200:
        print("Authentication Error:", response.text)
        raise Exception("Failed to authenticate with Salesforce Marketing Cloud API")

    return response.json().get('access_token')

def send_sms(access_token, phone_number, message, sms_send_id):
    """Send SMS using Marketing Cloud API."""
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    data = {
        "Subscribe": True,
        "Resubscribe": True,
        "mobileNumbers": [phone_number],
        "keyword": "NIC",
        "Override": True,
        "messageText": message
    }

    url = send_sms_url.format(sms_send_id=sms_send_id)
    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 202:
        return {"status": "success", "message": f"{response.status_code} - {response.text}"}
    else:
        return {"status": "error", "message": f"{response.status_code} - {response.text}"}

def get_sms_data_from_db(platform):
    """Retrieve SMS data from database for the specified platform."""
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        query = """
            SELECT id_sms, phone, email, firstname, lastname, version, message
            FROM subscriber.transaction_sms
            WHERE platform = %s AND campaign_date = CURRENT_DATE AND delivery_status IS NULL
        """
        cursor.execute(query, (platform,))
        records = cursor.fetchall()

        sms_data = []
        for record in records:
            id_sms, phone, email, firstname, lastname, version, message = record
            sms_data.append({
                'id_sms': id_sms,
                'phone': phone,
                'email': email,
                'firstname': firstname,
                'lastname': lastname,
                'version': version,
                'message': message
            })
        return sms_data

    except Exception as e:
        print("Error reading data from PostgreSQL table:", str(e))
        return []  # Return empty list instead of None
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def update_delivery_status(id_sms, status, delivery_date, response_mce):
    """Update delivery status in the database."""
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        update_query = """
            UPDATE subscriber.transaction_sms
            SET delivery_status = %s, delivery_date = %s, response_mce = %s
            WHERE id_sms = %s
        """
        cursor.execute(update_query, (status, delivery_date, response_mce, id_sms))
        connection.commit()
    except Exception as e:
        print("Error updating delivery status in PostgreSQL table:", str(e))
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def process_platform(platform_key):
    """Process SMS sending for a specific platform."""
    if platform_key not in PLATFORM_CONFIG:
        print(f"Error: Unknown platform '{platform_key}'. Available platforms: {', '.join(PLATFORM_CONFIG.keys())}")
        return False

    config = PLATFORM_CONFIG[platform_key]
    print(f"Processing platform: {platform_key}")

    try:
        # Authenticate
        access_token = authenticate(client_id, client_secret)

        # Get SMS data for this platform
        sms_data = get_sms_data_from_db(config['platform'])

        if not sms_data:
            print(f"No SMS data found for platform {platform_key}")
            return True

        print(f"Found {len(sms_data)} SMS records to process for {platform_key}")

        # Process each SMS
        for sms in sms_data:
            id_sms = sms['id_sms']
            phone = sms['phone']
            message = sms['message']

            # Add 1 prefix to phone number
            formatted_phone = f"1{phone}"
            result = send_sms(access_token, formatted_phone, message, config['sms_send_id'])

            status = result['status']
            response_mce = result['message']
            delivery_date = datetime.now().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('US/Pacific')) if status == "success" else None

            update_delivery_status(id_sms, status, delivery_date, response_mce)
            print(f"Processed SMS ID {id_sms}: {status}")

        return True

    except Exception as e:
        print(f"Error processing platform {platform_key}: {str(e)}")
        return False

def main():
    """Main function to process SMS sending."""
    if len(sys.argv) < 2:
        print("Usage: python mce.py <platform> [platform2] [platform3] ...")
        print(f"Available platforms: {', '.join(PLATFORM_CONFIG.keys())}")
        print("Use 'all' to process all platforms")
        sys.exit(1)

    platforms_to_process = sys.argv[1:]

    # Handle 'all' keyword
    if 'all' in platforms_to_process:
        platforms_to_process = list(PLATFORM_CONFIG.keys())

    success_count = 0
    total_count = len(platforms_to_process)

    for platform in platforms_to_process:
        if process_platform(platform):
            success_count += 1
        else:
            print(f"Failed to process platform: {platform}")

    print(f"\nProcessing complete: {success_count}/{total_count} platforms processed successfully")

if __name__ == "__main__":
    main()